'use client';

import { cn } from '@/lib';
import { useScopedLoading, useTranslation } from '@/contexts';
import { LOADING_SCOPES } from '@/constants';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface OptimizedImageProps {
	src: string;
	alt: string;
	width?: number;
	height?: number;
	className?: string;
	quality?: number;
	priority?: boolean;
	fill?: boolean;
	sizes?: string;
	onLoad?: () => void;
}

export function OptimizedImage({
	src,
	alt,
	width,
	height,
	className,
	quality = 85,
	priority = false,
	fill = false,
	sizes,
	onLoad,
}: OptimizedImageProps) {
	const { setLoading, getLoading } = useScopedLoading(LOADING_SCOPES.OPTIMIZED_IMAGE);
	const loading = getLoading('imageLoad');
	const [error, setError] = useState(false);
	const [imageSrc, setImageSrc] = useState(src);
	const { t } = useTranslation();

	// Reset state when src changes
	useEffect(() => {
		setLoading('imageLoad', true);
		setError(false);
		setImageSrc(src);
	}, [src, setLoading]);

	return (
		<div
			className={cn(
				'relative overflow-hidden',
				loading && 'bg-gray-100 animate-pulse',
				error && 'bg-gray-200',
				className
			)}
		>
			{!error ? (
				<Image
					src={imageSrc}
					alt={alt}
					width={!fill ? width : undefined}
					height={!fill ? height : undefined}
					quality={quality}
					priority={priority}
					fill={fill}
					sizes={sizes || '(max-width: 768px) 100vw, 50vw'}
					className={cn(
						'transition-opacity duration-300',
						loading ? 'opacity-0' : 'opacity-100'
					)}
					onLoad={() => {
						setLoading('imageLoad', false);
						onLoad?.();
					}}
					onError={() => {
						setLoading('imageLoad', false);
						setError(true);
					}}
				/>
			) : (
				<div className="absolute inset-0 flex items-center justify-center bg-gray-200 text-gray-500 text-sm">
					{t('ui.failed_to_load_image')}
				</div>
			)}
		</div>
	);
}
